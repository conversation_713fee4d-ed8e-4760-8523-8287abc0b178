// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}


// Validate ../../app/about/page.tsx
{
  const handler = {} as typeof import("../../app/about/page.js")
  handler satisfies AppPageConfig<"/about">
}

// Validate ../../app/contact/page.tsx
{
  const handler = {} as typeof import("../../app/contact/page.js")
  handler satisfies AppPageConfig<"/contact">
}

// Validate ../../app/page.tsx
{
  const handler = {} as typeof import("../../app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../app/services/consulting/page.tsx
{
  const handler = {} as typeof import("../../app/services/consulting/page.js")
  handler satisfies AppPageConfig<"/services/consulting">
}

// Validate ../../app/services/page.tsx
{
  const handler = {} as typeof import("../../app/services/page.js")
  handler satisfies AppPageConfig<"/services">
}

// Validate ../../app/services/supply-chain/page.tsx
{
  const handler = {} as typeof import("../../app/services/supply-chain/page.js")
  handler satisfies AppPageConfig<"/services/supply-chain">
}

// Validate ../../app/services/technology/page.tsx
{
  const handler = {} as typeof import("../../app/services/technology/page.js")
  handler satisfies AppPageConfig<"/services/technology">
}

// Validate ../../app/services/transport/page.tsx
{
  const handler = {} as typeof import("../../app/services/transport/page.js")
  handler satisfies AppPageConfig<"/services/transport">
}

// Validate ../../app/services/warehousing/page.tsx
{
  const handler = {} as typeof import("../../app/services/warehousing/page.js")
  handler satisfies AppPageConfig<"/services/warehousing">
}

// Validate ../../app/technology/page.tsx
{
  const handler = {} as typeof import("../../app/technology/page.js")
  handler satisfies AppPageConfig<"/technology">
}

// Validate ../../app/tracking/page.tsx
{
  const handler = {} as typeof import("../../app/tracking/page.js")
  handler satisfies AppPageConfig<"/tracking">
}







// Validate ../../app/layout.tsx
{
  const handler = {} as typeof import("../../app/layout.js")
  handler satisfies LayoutConfig<"/">
}
