import Link from 'next/link';
import { 
  BuildingOfficeIcon, 
  UserIcon,
  CalendarIcon,
  MapPinIcon,
  StarIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const values = [
  {
    name: 'Integrity',
    description: 'We conduct business with honesty, transparency, and ethical practices in all our operations.',
    icon: '🤝'
  },
  {
    name: 'Innovation',
    description: 'We continuously embrace new technologies and creative solutions to stay ahead of industry trends.',
    icon: '💡'
  },
  {
    name: 'Customer-Centricity',
    description: 'Our clients\' success is our priority, and we tailor solutions to meet their unique needs.',
    icon: '🎯'
  },
  {
    name: 'Excellence',
    description: 'We strive for the highest standards in service delivery and operational performance.',
    icon: '⭐'
  },
  {
    name: 'Sustainability',
    description: 'We are committed to environmentally responsible practices and sustainable business growth.',
    icon: '🌱'
  },
];

const milestones = [
  {
    year: '2025',
    title: 'Company Founded',
    description: 'Tshumisano Tech Logistics was established in Polokwane, Limpopo, with a vision to transform African logistics through technology.'
  },
  {
    year: '2025',
    title: 'Technology Platform Launch',
    description: 'Launched our proprietary logistics management platform, integrating AI and IoT technologies for enhanced supply chain visibility.'
  },
  {
    year: '2025',
    title: 'Strategic Partnerships',
    description: 'Formed key partnerships with leading technology providers and logistics companies across Africa.'
  },
  {
    year: 'Future',
    title: 'Continental Expansion',
    description: 'Planning expansion across major African markets to become the continent\'s leading tech-enabled logistics provider.'
  }
];

const leadership = [
  {
    name: 'Mr. Vusani Tshisikule',
    position: 'Director & Founder',
    description: 'Visionary leader with extensive experience in logistics and technology integration across African markets.',
    image: '/leadership/vusani.jpg' // Placeholder
  }
];

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 via-white to-secondary-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              About
              <span className="text-gradient block">Tshumisano Tech Logistics</span>
            </h1>
            <p className="text-xl text-neutral-600 mb-8 leading-relaxed">
              Founded in 2025 and headquartered in Polokwane, Limpopo, we are pioneering 
              the future of logistics in Africa through innovative technology solutions 
              and exceptional service delivery.
            </p>
          </div>
        </div>
      </section>

      {/* Company Overview */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-8">
                Our Story
              </h2>
              <div className="space-y-6 text-neutral-600 leading-relaxed">
                <p>
                  Tshumisano Tech Logistics (Pty) Ltd was born from a vision to revolutionize 
                  the logistics landscape across Africa. Founded in 2025 in the heart of 
                  Limpopo, South Africa, we recognized the immense potential for technology 
                  to transform traditional logistics operations.
                </p>
                <p>
                  Our name "Tshumisano" means "cooperation" in Tshivenda, reflecting our 
                  commitment to collaborative partnerships with our clients, suppliers, 
                  and communities. We believe that through cooperation and cutting-edge 
                  technology, we can build more efficient, sustainable, and inclusive 
                  supply chains across the continent.
                </p>
                <p>
                  From our headquarters in Polokwane, we serve clients across Africa, 
                  providing comprehensive logistics solutions that combine local expertise 
                  with global best practices and innovative technology platforms.
                </p>
              </div>
            </div>
            <div className="bg-neutral-50 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-neutral-900 mb-6">Company Details</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <BuildingOfficeIcon className="h-6 w-6 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-neutral-900">Registration</h4>
                    <p className="text-sm text-neutral-600">2025/610616/07</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CalendarIcon className="h-6 w-6 text-secondary-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-neutral-900">Founded</h4>
                    <p className="text-sm text-neutral-600">2025</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <MapPinIcon className="h-6 w-6 text-accent-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-neutral-900">Headquarters</h4>
                    <p className="text-sm text-neutral-600">
                      42 Eaglescrest Boulevard<br />
                      Eaglescrest Estate, Bendor<br />
                      Polokwane, Limpopo, 0699
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <UserIcon className="h-6 w-6 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-neutral-900">Director</h4>
                    <p className="text-sm text-neutral-600">Mr. Vusani Tshisikule</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="bg-neutral-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Our Mission & Vision
            </h2>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="h-16 w-16 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="text-2xl font-bold text-center text-neutral-900 mb-6">Our Mission</h3>
              <p className="text-neutral-600 leading-relaxed text-center">
                To deliver smart, seamless, scalable logistics and technology solutions 
                that empower businesses across Africa to achieve operational excellence 
                and sustainable growth through innovative supply chain management.
              </p>
            </div>
            
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="h-16 w-16 bg-secondary-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl">🚀</span>
              </div>
              <h3 className="text-2xl font-bold text-center text-neutral-900 mb-6">Our Vision</h3>
              <p className="text-neutral-600 leading-relaxed text-center">
                To become Africa's most trusted leader in technology-enabled logistics, 
                connecting communities and driving economic transformation through 
                innovative, sustainable, and inclusive supply chain solutions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Our Core Values
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              These fundamental principles guide every decision we make and every solution we deliver.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <div key={value.name} className="text-center">
                <div className="h-20 w-20 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <span className="text-3xl">{value.icon}</span>
                </div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-4">{value.name}</h3>
                <p className="text-neutral-600 leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Leadership Section */}
      <section id="leadership" className="bg-neutral-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Our Leadership
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              Meet the visionary leader driving innovation and excellence in African logistics.
            </p>
          </div>

          <div className="max-w-2xl mx-auto">
            {leadership.map((leader) => (
              <div key={leader.name} className="bg-white rounded-2xl p-8 shadow-sm text-center">
                <div className="h-32 w-32 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-4xl font-bold text-white">VT</span>
                </div>
                <h3 className="text-2xl font-bold text-neutral-900 mb-2">{leader.name}</h3>
                <p className="text-lg text-primary-600 font-medium mb-4">{leader.position}</p>
                <p className="text-neutral-600 leading-relaxed">{leader.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Our Journey
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              From our founding to our future vision, discover the key milestones
              that define our growth and commitment to excellence.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <div key={index} className="flex items-start">
                  <div className="flex-shrink-0 mr-6">
                    <div className="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{milestone.year}</span>
                    </div>
                  </div>
                  <div className="flex-grow">
                    <h3 className="text-xl font-semibold text-neutral-900 mb-2">{milestone.title}</h3>
                    <p className="text-neutral-600 leading-relaxed">{milestone.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-primary-600 to-secondary-600 text-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Partner with Us?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join us in transforming the future of logistics in Africa.
              Let's build something extraordinary together.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="bg-white text-primary-600 hover:bg-neutral-100 font-medium py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center gap-2">
                Get in Touch
                <ArrowRightIcon className="h-5 w-5" />
              </Link>
              <Link href="/services" className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-4 px-8 rounded-lg transition-all duration-200">
                Explore Our Services
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
