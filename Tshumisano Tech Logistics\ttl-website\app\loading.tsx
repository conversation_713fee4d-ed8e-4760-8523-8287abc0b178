export default function Loading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex items-center justify-center">
      <div className="text-center">
        <div className="h-20 w-20 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-8 animate-pulse">
          <span className="text-white font-bold text-2xl">TTL</span>
        </div>
        <div className="flex items-center justify-center space-x-2">
          <div className="h-3 w-3 bg-primary-600 rounded-full animate-bounce"></div>
          <div className="h-3 w-3 bg-secondary-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="h-3 w-3 bg-accent-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <p className="text-neutral-600 mt-4">Loading...</p>
      </div>
    </div>
  );
}
