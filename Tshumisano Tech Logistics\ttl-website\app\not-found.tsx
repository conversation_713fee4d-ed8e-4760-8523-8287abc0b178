import Link from 'next/link';
import { 
  HomeIcon,
  ArrowRightIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex items-center justify-center">
      <div className="max-w-2xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <div className="h-32 w-32 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-8">
          <ExclamationTriangleIcon className="h-16 w-16 text-secondary-600" />
        </div>
        
        <h1 className="text-6xl md:text-8xl font-bold text-neutral-900 mb-4">404</h1>
        <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-6">
          Page Not Found
        </h2>
        <p className="text-xl text-neutral-600 mb-8 leading-relaxed">
          Oops! It looks like this page has been moved or doesn't exist. 
          Don't worry, our logistics experts can help you find what you're looking for.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/" className="btn-primary text-lg px-8 py-4">
            <HomeIcon className="h-5 w-5" />
            Back to Home
          </Link>
          <Link href="/services" className="btn-outline text-lg px-8 py-4">
            View Services
            <ArrowRightIcon className="h-5 w-5" />
          </Link>
        </div>
        
        <div className="mt-12 p-6 bg-white rounded-xl shadow-sm">
          <h3 className="text-lg font-semibold text-neutral-900 mb-4">
            Need Help Finding Something?
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <Link href="/services" className="text-primary-600 hover:text-primary-700 transition-colors">
              Our Services
            </Link>
            <Link href="/about" className="text-primary-600 hover:text-primary-700 transition-colors">
              About Us
            </Link>
            <Link href="/contact" className="text-primary-600 hover:text-primary-700 transition-colors">
              Contact Us
            </Link>
            <Link href="/technology" className="text-primary-600 hover:text-primary-700 transition-colors">
              Technology
            </Link>
            <Link href="/tracking" className="text-primary-600 hover:text-primary-700 transition-colors">
              Track Shipment
            </Link>
            <Link href="/services/transport" className="text-primary-600 hover:text-primary-700 transition-colors">
              Transport Services
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
