import Link from 'next/link';
import { 
  UserGroupIcon, 
  AcademicCapIcon,
  ClipboardDocumentCheckIcon,
  ShieldCheckIcon,
  ChatBubbleLeftRightIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const consultingServices = [
  {
    name: 'Strategic Planning',
    description: 'Comprehensive strategic planning and analysis to optimize your logistics operations.',
    icon: ClipboardDocumentCheckIcon,
    features: [
      'Logistics Strategy Development',
      'Network Design & Optimization',
      'Cost-Benefit Analysis',
      'Implementation Roadmaps',
      'Performance Benchmarking',
      'ROI Assessment'
    ]
  },
  {
    name: 'Training & Development',
    description: 'Comprehensive training programs to enhance your team\'s logistics capabilities.',
    icon: AcademicCapIcon,
    features: [
      'Logistics Management Training',
      'Technology Platform Training',
      'Best Practices Workshops',
      'Certification Programs',
      'Custom Training Modules',
      'Ongoing Skills Development'
    ]
  },
  {
    name: 'Process Improvement',
    description: 'Expert consulting to identify inefficiencies and implement process improvements.',
    icon: ChatBubbleLeftRightIcon,
    features: [
      'Process Analysis & Mapping',
      'Bottleneck Identification',
      'Workflow Optimization',
      'Quality Improvement',
      'Change Management',
      'Continuous Improvement'
    ]
  },
  {
    name: 'Compliance & Risk',
    description: 'Regulatory compliance guidance and risk management consulting services.',
    icon: ShieldCheckIcon,
    features: [
      'Regulatory Compliance',
      'Risk Assessment',
      'Audit Preparation',
      'Policy Development',
      'Safety Management',
      'Documentation Support'
    ]
  }
];

export default function ConsultingPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 via-white to-secondary-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="h-20 w-20 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-8">
              <UserGroupIcon className="h-10 w-10 text-primary-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Consulting &
              <span className="text-gradient block">Support Services</span>
            </h1>
            <p className="text-xl text-neutral-600 mb-8 leading-relaxed">
              Expert advisory services and ongoing support to help you navigate complex 
              logistics challenges and unlock new opportunities for growth and efficiency.
            </p>
            <Link href="/contact" className="btn-primary text-lg px-8 py-4">
              Schedule Consultation
              <ArrowRightIcon className="h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Our Consulting Services
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              From strategic planning to hands-on training, we provide comprehensive 
              consulting services to help you achieve operational excellence.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {consultingServices.map((service) => (
              <div key={service.name} className="bg-neutral-50 rounded-xl p-8">
                <div className="flex items-center mb-6">
                  <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                    <service.icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-neutral-900">{service.name}</h3>
                </div>
                <p className="text-neutral-600 mb-6 leading-relaxed">
                  {service.description}
                </p>
                <ul className="space-y-2">
                  {service.features.map((feature) => (
                    <li key={feature} className="flex items-center text-sm text-neutral-600">
                      <CheckCircleIcon className="h-4 w-4 text-accent-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Support Section */}
      <section className="bg-neutral-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-8">
                24/7 Support & Assistance
              </h2>
              <div className="space-y-6">
                <div className="flex items-start">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-2">Round-the-Clock Support</h3>
                    <p className="text-neutral-600">Our expert support team is available 24/7 to assist with any issues or questions</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <AcademicCapIcon className="h-6 w-6 text-secondary-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-2">Expert Guidance</h3>
                    <p className="text-neutral-600">Access to experienced logistics professionals and industry experts</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <ShieldCheckIcon className="h-6 w-6 text-accent-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-2">Proactive Monitoring</h3>
                    <p className="text-neutral-600">Continuous monitoring and proactive issue resolution to prevent disruptions</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-br from-primary-600 to-secondary-600 rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-6">Support Channels</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-bold">📞</span>
                  </div>
                  <span>Phone Support (24/7)</span>
                </div>
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-bold">💬</span>
                  </div>
                  <span>Live Chat Support</span>
                </div>
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-bold">📧</span>
                  </div>
                  <span>Email Support</span>
                </div>
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-bold">🎓</span>
                  </div>
                  <span>Training Portal</span>
                </div>
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-bold">📚</span>
                  </div>
                  <span>Knowledge Base</span>
                </div>
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-bold">🔧</span>
                  </div>
                  <span>Remote Assistance</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-6">
              Need Expert Logistics Guidance?
            </h2>
            <p className="text-xl text-neutral-600 mb-8">
              Our experienced consultants are ready to help you overcome challenges, 
              optimize operations, and achieve your logistics goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary text-lg px-8 py-4">
                Book Consultation
                <ArrowRightIcon className="h-5 w-5" />
              </Link>
              <Link href="/services" className="btn-outline text-lg px-8 py-4">
                View All Services
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
