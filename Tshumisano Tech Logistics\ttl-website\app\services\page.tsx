import Link from 'next/link';
import { 
  TruckIcon, 
  CogIcon, 
  BuildingOfficeIcon, 
  ComputerDesktopIcon,
  UserGroupIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const services = [
  {
    name: 'Transport & Logistics',
    description: 'Comprehensive freight, distribution, and last-mile delivery solutions designed to move your goods efficiently across Africa.',
    icon: TruckIcon,
    href: '/services/transport',
    features: [
      'Freight Management & Coordination',
      'Multi-modal Transportation',
      'Distribution Networks',
      'Last-Mile Delivery Solutions',
      'Cross-border Logistics',
      'Real-time Tracking & Monitoring'
    ],
    benefits: [
      'Reduced transportation costs',
      'Improved delivery times',
      'Enhanced supply chain visibility',
      'Scalable logistics solutions'
    ]
  },
  {
    name: 'Supply Chain Management',
    description: 'Technology-driven operations to optimize your entire supply chain ecosystem from procurement to delivery.',
    icon: CogIcon,
    href: '/services/supply-chain',
    features: [
      'End-to-end Process Optimization',
      'Inventory Management Systems',
      'Demand Planning & Forecasting',
      'Supplier Relationship Management',
      'Risk Management & Mitigation',
      'Performance Analytics & Reporting'
    ],
    benefits: [
      'Streamlined operations',
      'Reduced inventory costs',
      'Better demand forecasting',
      'Enhanced supplier relationships'
    ]
  },
  {
    name: 'Warehousing & Storage',
    description: 'Safe, efficient, and scalable storage facilities equipped with advanced management systems and security protocols.',
    icon: BuildingOfficeIcon,
    href: '/services/warehousing',
    features: [
      'Secure Storage Facilities',
      'Automated Inventory Tracking',
      'Climate-controlled Environments',
      'Distribution Centers',
      'Pick & Pack Services',
      'Quality Control Systems'
    ],
    benefits: [
      'Reduced storage costs',
      'Improved inventory accuracy',
      'Enhanced security',
      'Flexible storage solutions'
    ]
  },
  {
    name: 'Technology Integration',
    description: 'Cutting-edge software and digital solutions designed to modernize and streamline your logistics operations.',
    icon: ComputerDesktopIcon,
    href: '/services/technology',
    features: [
      'Digital Platform Development',
      'IoT Solutions & Sensors',
      'Data Analytics & Intelligence',
      'Mobile Applications',
      'API Integrations',
      'Cloud-based Solutions'
    ],
    benefits: [
      'Increased operational efficiency',
      'Real-time visibility',
      'Data-driven decisions',
      'Seamless integrations'
    ]
  },
  {
    name: 'Consulting & Support',
    description: 'Expert advisory services and ongoing support to help you navigate complex logistics challenges and opportunities.',
    icon: UserGroupIcon,
    href: '/services/consulting',
    features: [
      'Strategic Planning & Analysis',
      '24/7 Technical Support',
      'Training & Development Programs',
      'Process Improvement Consulting',
      'Compliance & Regulatory Guidance',
      'Change Management Support'
    ],
    benefits: [
      'Expert guidance',
      'Continuous improvement',
      'Reduced operational risks',
      'Enhanced team capabilities'
    ]
  },
];

export default function ServicesPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 via-white to-secondary-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Comprehensive Logistics
              <span className="text-gradient block">Solutions</span>
            </h1>
            <p className="text-xl text-neutral-600 mb-8 leading-relaxed">
              From transport and warehousing to cutting-edge technology integration, 
              we provide end-to-end logistics solutions tailored to your business needs.
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {services.map((service, index) => (
              <div key={service.name} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                  <div className="flex items-center mb-6">
                    <div className="h-16 w-16 bg-primary-100 rounded-xl flex items-center justify-center mr-6">
                      <service.icon className="h-8 w-8 text-primary-600" />
                    </div>
                    <h2 className="text-3xl font-bold text-neutral-900">{service.name}</h2>
                  </div>
                  <p className="text-lg text-neutral-600 mb-8 leading-relaxed">
                    {service.description}
                  </p>
                  <Link 
                    href={service.href}
                    className="btn-primary mb-8"
                  >
                    Learn More
                    <ArrowRightIcon className="h-5 w-5" />
                  </Link>
                </div>
                
                <div className={`bg-neutral-50 rounded-2xl p-8 ${index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}`}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-lg font-semibold text-neutral-900 mb-4">Key Features</h3>
                      <ul className="space-y-3">
                        {service.features.map((feature) => (
                          <li key={feature} className="flex items-start text-sm text-neutral-600">
                            <CheckCircleIcon className="h-4 w-4 text-accent-500 mr-2 mt-0.5 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-neutral-900 mb-4">Benefits</h3>
                      <ul className="space-y-3">
                        {service.benefits.map((benefit) => (
                          <li key={benefit} className="flex items-start text-sm text-neutral-600">
                            <CheckCircleIcon className="h-4 w-4 text-primary-500 mr-2 mt-0.5 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-primary-600 to-secondary-600 text-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Optimize Your Operations?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Let our experts help you choose the right combination of services 
              to transform your logistics and supply chain operations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="bg-white text-primary-600 hover:bg-neutral-100 font-medium py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center gap-2">
                Get Custom Quote
                <ArrowRightIcon className="h-5 w-5" />
              </Link>
              <Link href="/technology" className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-4 px-8 rounded-lg transition-all duration-200">
                Explore Technology
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
