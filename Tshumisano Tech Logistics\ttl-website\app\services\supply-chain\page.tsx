import Link from 'next/link';
import { 
  CogIcon, 
  ChartBarIcon,
  ClipboardDocumentListIcon,
  UserGroupIcon,
  ExclamationTriangleIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const supplyChainServices = [
  {
    name: 'Process Optimization',
    description: 'Streamline your supply chain processes for maximum efficiency and cost reduction.',
    icon: CogIcon,
    features: [
      'End-to-end Process Analysis',
      'Workflow Automation',
      'Bottleneck Identification',
      'Performance Metrics',
      'Continuous Improvement',
      'Best Practice Implementation'
    ]
  },
  {
    name: 'Inventory Management',
    description: 'Advanced inventory control systems to optimize stock levels and reduce costs.',
    icon: ClipboardDocumentListIcon,
    features: [
      'Real-time Inventory Tracking',
      'Automated Reorder Points',
      'ABC Analysis',
      'Stock Level Optimization',
      'Cycle Counting Programs',
      'Inventory Forecasting'
    ]
  },
  {
    name: 'Demand Planning',
    description: 'Sophisticated forecasting and planning tools to predict and meet customer demand.',
    icon: ChartBarIcon,
    features: [
      'Statistical Forecasting',
      'Seasonal Demand Analysis',
      'Market Trend Integration',
      'Collaborative Planning',
      'Scenario Modeling',
      'Demand Sensing'
    ]
  },
  {
    name: 'Supplier Management',
    description: 'Comprehensive supplier relationship and performance management solutions.',
    icon: UserGroupIcon,
    features: [
      'Supplier Evaluation',
      'Performance Monitoring',
      'Contract Management',
      'Risk Assessment',
      'Collaboration Platforms',
      'Vendor Development'
    ]
  }
];

export default function SupplyChainPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 via-white to-secondary-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="h-20 w-20 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-8">
              <CogIcon className="h-10 w-10 text-primary-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Supply Chain
              <span className="text-gradient block">Management</span>
            </h1>
            <p className="text-xl text-neutral-600 mb-8 leading-relaxed">
              Technology-driven operations to optimize your entire supply chain ecosystem 
              from procurement to delivery, ensuring maximum efficiency and cost-effectiveness.
            </p>
            <Link href="/contact" className="btn-primary text-lg px-8 py-4">
              Optimize My Supply Chain
              <ArrowRightIcon className="h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Our Supply Chain Solutions
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              Comprehensive supply chain management services designed to enhance 
              visibility, reduce costs, and improve operational performance.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {supplyChainServices.map((service) => (
              <div key={service.name} className="bg-neutral-50 rounded-xl p-8">
                <div className="flex items-center mb-6">
                  <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                    <service.icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-neutral-900">{service.name}</h3>
                </div>
                <p className="text-neutral-600 mb-6 leading-relaxed">
                  {service.description}
                </p>
                <ul className="space-y-2">
                  {service.features.map((feature) => (
                    <li key={feature} className="flex items-center text-sm text-neutral-600">
                      <CheckCircleIcon className="h-4 w-4 text-accent-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="bg-neutral-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-8">
                Transform Your Supply Chain
              </h2>
              <div className="space-y-6">
                <div className="flex items-start">
                  <ChartBarIcon className="h-6 w-6 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-2">Increased Efficiency</h3>
                    <p className="text-neutral-600">Streamline operations and eliminate waste through process optimization</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <ExclamationTriangleIcon className="h-6 w-6 text-secondary-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-2">Risk Mitigation</h3>
                    <p className="text-neutral-600">Identify and manage supply chain risks before they impact your business</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <UserGroupIcon className="h-6 w-6 text-accent-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-2">Better Collaboration</h3>
                    <p className="text-neutral-600">Enhance communication and coordination across your supply network</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-br from-primary-600 to-secondary-600 rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-6">Key Outcomes</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-bold">1</span>
                  </div>
                  <span>Reduced operational costs by up to 25%</span>
                </div>
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-bold">2</span>
                  </div>
                  <span>Improved inventory turnover rates</span>
                </div>
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-bold">3</span>
                  </div>
                  <span>Enhanced customer satisfaction</span>
                </div>
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-bold">4</span>
                  </div>
                  <span>Better demand forecast accuracy</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-6">
              Ready to Optimize Your Supply Chain?
            </h2>
            <p className="text-xl text-neutral-600 mb-8">
              Let our supply chain experts analyze your current operations and 
              design a customized solution to drive efficiency and growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary text-lg px-8 py-4">
                Schedule Consultation
                <ArrowRightIcon className="h-5 w-5" />
              </Link>
              <Link href="/services" className="btn-outline text-lg px-8 py-4">
                View All Services
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
