import Link from 'next/link';
import { 
  ComputerDesktopIcon, 
  DevicePhoneMobileIcon,
  CloudIcon,
  ChartBarIcon,
  CogIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const technologyServices = [
  {
    name: 'Digital Platforms',
    description: 'Custom-built digital platforms to streamline your logistics operations and enhance visibility.',
    icon: ComputerDesktopIcon,
    features: [
      'Web-based Dashboards',
      'Custom Portal Development',
      'Integration Capabilities',
      'User Management Systems',
      'Reporting & Analytics',
      'Mobile Responsiveness'
    ]
  },
  {
    name: 'Mobile Applications',
    description: 'Native and cross-platform mobile apps for on-the-go logistics management.',
    icon: DevicePhoneMobileIcon,
    features: [
      'Driver Mobile Apps',
      'Customer Tracking Apps',
      'Warehouse Management Apps',
      'Proof of Delivery Systems',
      'Real-time Notifications',
      'Offline Capabilities'
    ]
  },
  {
    name: 'Cloud Solutions',
    description: 'Scalable cloud-based infrastructure and software solutions for modern logistics.',
    icon: CloudIcon,
    features: [
      'Cloud Migration Services',
      'SaaS Solutions',
      'Data Backup & Recovery',
      'Scalable Infrastructure',
      'Security & Compliance',
      'Cost Optimization'
    ]
  },
  {
    name: 'Data Analytics',
    description: 'Advanced analytics and business intelligence tools to drive data-driven decisions.',
    icon: ChartBarIcon,
    features: [
      'Performance Dashboards',
      'Predictive Analytics',
      'Route Optimization',
      'Cost Analysis',
      'KPI Monitoring',
      'Custom Reports'
    ]
  }
];

export default function TechnologyPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 via-white to-secondary-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="h-20 w-20 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-8">
              <ComputerDesktopIcon className="h-10 w-10 text-primary-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Technology
              <span className="text-gradient block">Integration</span>
            </h1>
            <p className="text-xl text-neutral-600 mb-8 leading-relaxed">
              Cutting-edge software and digital solutions designed to modernize and 
              streamline your logistics operations with the latest technology innovations.
            </p>
            <Link href="/contact" className="btn-primary text-lg px-8 py-4">
              Explore Tech Solutions
              <ArrowRightIcon className="h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Our Technology Solutions
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              From custom software development to advanced analytics, we provide 
              comprehensive technology solutions to digitize your logistics operations.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {technologyServices.map((service) => (
              <div key={service.name} className="bg-neutral-50 rounded-xl p-8">
                <div className="flex items-center mb-6">
                  <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                    <service.icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-neutral-900">{service.name}</h3>
                </div>
                <p className="text-neutral-600 mb-6 leading-relaxed">
                  {service.description}
                </p>
                <ul className="space-y-2">
                  {service.features.map((feature) => (
                    <li key={feature} className="flex items-center text-sm text-neutral-600">
                      <CheckCircleIcon className="h-4 w-4 text-accent-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Technology Stack Section */}
      <section className="bg-neutral-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Our Technology Stack
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              We leverage the latest technologies and industry best practices to deliver 
              robust, scalable, and secure solutions.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-8 text-center">
              <div className="h-16 w-16 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                <CogIcon className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-neutral-900 mb-4">Backend Systems</h3>
              <ul className="text-sm text-neutral-600 space-y-2">
                <li>Node.js & Python</li>
                <li>Microservices Architecture</li>
                <li>RESTful APIs</li>
                <li>Database Management</li>
                <li>Message Queues</li>
              </ul>
            </div>
            
            <div className="bg-white rounded-xl p-8 text-center">
              <div className="h-16 w-16 bg-secondary-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                <DevicePhoneMobileIcon className="h-8 w-8 text-secondary-600" />
              </div>
              <h3 className="text-xl font-semibold text-neutral-900 mb-4">Frontend & Mobile</h3>
              <ul className="text-sm text-neutral-600 space-y-2">
                <li>React & Next.js</li>
                <li>React Native</li>
                <li>Progressive Web Apps</li>
                <li>Responsive Design</li>
                <li>Real-time Updates</li>
              </ul>
            </div>
            
            <div className="bg-white rounded-xl p-8 text-center">
              <div className="h-16 w-16 bg-accent-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                <CloudIcon className="h-8 w-8 text-accent-600" />
              </div>
              <h3 className="text-xl font-semibold text-neutral-900 mb-4">Cloud & DevOps</h3>
              <ul className="text-sm text-neutral-600 space-y-2">
                <li>AWS & Azure</li>
                <li>Docker & Kubernetes</li>
                <li>CI/CD Pipelines</li>
                <li>Monitoring & Logging</li>
                <li>Security & Compliance</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-6">
              Ready to Digitize Your Operations?
            </h2>
            <p className="text-xl text-neutral-600 mb-8">
              Let our technology experts help you identify opportunities for digital 
              transformation and build custom solutions for your business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary text-lg px-8 py-4">
                Start Digital Transformation
                <ArrowRightIcon className="h-5 w-5" />
              </Link>
              <Link href="/services" className="btn-outline text-lg px-8 py-4">
                View All Services
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
