import Link from 'next/link';
import { 
  TruckIcon, 
  MapPinIcon,
  ClockIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const transportServices = [
  {
    name: 'Freight Management',
    description: 'Comprehensive freight coordination and management services for all cargo types.',
    icon: TruckIcon,
    features: [
      'Full Truckload (FTL) Services',
      'Less Than Truckload (LTL) Options',
      'Specialized Cargo Handling',
      'Temperature-controlled Transport',
      'Hazardous Materials Transport',
      'Oversized Cargo Solutions'
    ]
  },
  {
    name: 'Distribution Networks',
    description: 'Extensive distribution networks covering major routes across Africa.',
    icon: MapPinIcon,
    features: [
      'Regional Distribution Centers',
      'Hub-and-spoke Networks',
      'Cross-docking Facilities',
      'Route Optimization',
      'Multi-modal Integration',
      'Strategic Location Planning'
    ]
  },
  {
    name: 'Last-Mile Delivery',
    description: 'Efficient final-mile delivery solutions to reach your customers.',
    icon: ClockIcon,
    features: [
      'Same-day Delivery Options',
      'Scheduled Delivery Windows',
      'Proof of Delivery Systems',
      'Customer Notification Services',
      'Return Logistics',
      'Urban Delivery Solutions'
    ]
  },
  {
    name: 'Cross-border Logistics',
    description: 'Seamless international transport solutions across African borders.',
    icon: GlobeAltIcon,
    features: [
      'Customs Clearance Support',
      'Documentation Management',
      'Border Crossing Coordination',
      'Compliance Management',
      'Multi-country Routing',
      'Trade Facilitation Services'
    ]
  }
];

const benefits = [
  'Reduced transportation costs through optimized routing',
  'Improved delivery reliability and on-time performance',
  'Enhanced cargo security and tracking capabilities',
  'Scalable solutions that grow with your business',
  'Expert handling of complex logistics challenges',
  'Comprehensive insurance and risk management'
];

export default function TransportPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 via-white to-secondary-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="h-20 w-20 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-8">
              <TruckIcon className="h-10 w-10 text-primary-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Transport & Logistics
              <span className="text-gradient block">Solutions</span>
            </h1>
            <p className="text-xl text-neutral-600 mb-8 leading-relaxed">
              Comprehensive freight, distribution, and last-mile delivery solutions 
              designed to move your goods efficiently and securely across Africa.
            </p>
            <Link href="/contact" className="btn-primary text-lg px-8 py-4">
              Get Transport Quote
              <ArrowRightIcon className="h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Our Transport Services
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              From local deliveries to continental freight, we provide comprehensive 
              transport solutions tailored to your specific requirements.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {transportServices.map((service) => (
              <div key={service.name} className="bg-neutral-50 rounded-xl p-8">
                <div className="flex items-center mb-6">
                  <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                    <service.icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-neutral-900">{service.name}</h3>
                </div>
                <p className="text-neutral-600 mb-6 leading-relaxed">
                  {service.description}
                </p>
                <ul className="space-y-2">
                  {service.features.map((feature) => (
                    <li key={feature} className="flex items-center text-sm text-neutral-600">
                      <CheckCircleIcon className="h-4 w-4 text-accent-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="bg-neutral-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-8">
                Why Choose Our Transport Solutions?
              </h2>
              <ul className="space-y-4">
                {benefits.map((benefit) => (
                  <li key={benefit} className="flex items-start">
                    <CheckCircleIcon className="h-6 w-6 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-neutral-600 leading-relaxed">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="bg-gradient-to-br from-primary-600 to-secondary-600 rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-6">Key Features</h3>
              <div className="space-y-6">
                <div className="flex items-start">
                  <ShieldCheckIcon className="h-6 w-6 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold mb-1">Real-time Tracking</h4>
                    <p className="text-sm opacity-90">Monitor your shipments 24/7 with our advanced tracking systems</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <ShieldCheckIcon className="h-6 w-6 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold mb-1">Secure Transport</h4>
                    <p className="text-sm opacity-90">Advanced security measures to protect your valuable cargo</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <ShieldCheckIcon className="h-6 w-6 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold mb-1">Flexible Solutions</h4>
                    <p className="text-sm opacity-90">Customizable transport options to meet your specific needs</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-6">
              Ready to Streamline Your Transport Operations?
            </h2>
            <p className="text-xl text-neutral-600 mb-8">
              Contact our transport specialists to discuss your specific requirements 
              and get a customized solution for your business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary text-lg px-8 py-4">
                Get Quote Now
                <ArrowRightIcon className="h-5 w-5" />
              </Link>
              <Link href="/services" className="btn-outline text-lg px-8 py-4">
                View All Services
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
