import Link from 'next/link';
import { 
  BuildingOfficeIcon, 
  ShieldCheckIcon,
  ClipboardDocumentCheckIcon,
  TruckIcon,
  CubeIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const warehousingServices = [
  {
    name: 'Secure Storage',
    description: 'State-of-the-art storage facilities with advanced security and climate control systems.',
    icon: ShieldCheckIcon,
    features: [
      '24/7 Security Monitoring',
      'Climate-controlled Environments',
      'Fire Suppression Systems',
      'Access Control Systems',
      'Insurance Coverage',
      'Pest Control Management'
    ]
  },
  {
    name: 'Inventory Management',
    description: 'Advanced inventory tracking and management systems for real-time visibility.',
    icon: ClipboardDocumentCheckIcon,
    features: [
      'Real-time Inventory Tracking',
      'Barcode & RFID Systems',
      'Cycle Counting Programs',
      'Stock Level Alerts',
      'Inventory Reporting',
      'Integration Capabilities'
    ]
  },
  {
    name: 'Distribution Services',
    description: 'Comprehensive pick, pack, and shipping services to streamline your operations.',
    icon: TruckIcon,
    features: [
      'Order Fulfillment',
      'Pick & Pack Services',
      'Kitting & Assembly',
      'Quality Control Checks',
      'Shipping Coordination',
      'Returns Processing'
    ]
  },
  {
    name: 'Specialized Storage',
    description: 'Customized storage solutions for specialized products and requirements.',
    icon: CubeIcon,
    features: [
      'Hazardous Materials Storage',
      'Temperature-sensitive Goods',
      'High-value Item Security',
      'Bulk Storage Solutions',
      'Pharmaceutical Storage',
      'Food-grade Facilities'
    ]
  }
];

export default function WarehousingPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 via-white to-secondary-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="h-20 w-20 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-8">
              <BuildingOfficeIcon className="h-10 w-10 text-primary-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Warehousing &
              <span className="text-gradient block">Storage Solutions</span>
            </h1>
            <p className="text-xl text-neutral-600 mb-8 leading-relaxed">
              Safe, efficient, and scalable storage facilities equipped with advanced 
              management systems and security protocols to protect your valuable inventory.
            </p>
            <Link href="/contact" className="btn-primary text-lg px-8 py-4">
              Get Storage Quote
              <ArrowRightIcon className="h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Our Warehousing Services
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              From basic storage to complex distribution operations, we provide 
              comprehensive warehousing solutions tailored to your specific needs.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {warehousingServices.map((service) => (
              <div key={service.name} className="bg-neutral-50 rounded-xl p-8">
                <div className="flex items-center mb-6">
                  <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                    <service.icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-neutral-900">{service.name}</h3>
                </div>
                <p className="text-neutral-600 mb-6 leading-relaxed">
                  {service.description}
                </p>
                <ul className="space-y-2">
                  {service.features.map((feature) => (
                    <li key={feature} className="flex items-center text-sm text-neutral-600">
                      <CheckCircleIcon className="h-4 w-4 text-accent-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-neutral-50 section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-8">
                Why Choose Our Warehousing?
              </h2>
              <div className="space-y-6">
                <div className="flex items-start">
                  <ShieldCheckIcon className="h-6 w-6 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-2">Maximum Security</h3>
                    <p className="text-neutral-600">Advanced security systems and protocols to protect your inventory</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <ClipboardDocumentCheckIcon className="h-6 w-6 text-secondary-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-2">Real-time Visibility</h3>
                    <p className="text-neutral-600">Complete transparency with real-time inventory tracking and reporting</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CubeIcon className="h-6 w-6 text-accent-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-neutral-900 mb-2">Flexible Solutions</h3>
                    <p className="text-neutral-600">Scalable storage options that adapt to your changing business needs</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-br from-primary-600 to-secondary-600 rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-6">Facility Features</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 mr-3 flex-shrink-0" />
                  <span>Modern warehouse facilities</span>
                </div>
                <div className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 mr-3 flex-shrink-0" />
                  <span>Strategic locations across Africa</span>
                </div>
                <div className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 mr-3 flex-shrink-0" />
                  <span>Advanced WMS technology</span>
                </div>
                <div className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 mr-3 flex-shrink-0" />
                  <span>Trained warehouse personnel</span>
                </div>
                <div className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 mr-3 flex-shrink-0" />
                  <span>Flexible contract terms</span>
                </div>
                <div className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 mr-3 flex-shrink-0" />
                  <span>Comprehensive insurance coverage</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-white section-padding">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-6">
              Need Reliable Storage Solutions?
            </h2>
            <p className="text-xl text-neutral-600 mb-8">
              Contact our warehousing specialists to discuss your storage requirements 
              and find the perfect solution for your business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact" className="btn-primary text-lg px-8 py-4">
                Request Storage Quote
                <ArrowRightIcon className="h-5 w-5" />
              </Link>
              <Link href="/services" className="btn-outline text-lg px-8 py-4">
                View All Services
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
