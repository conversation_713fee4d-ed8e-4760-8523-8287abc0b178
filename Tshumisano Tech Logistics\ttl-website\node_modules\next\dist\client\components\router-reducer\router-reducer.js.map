{"version": 3, "sources": ["../../../../src/client/components/router-reducer/router-reducer.ts"], "sourcesContent": ["import {\n  ACTION_NAVIGATE,\n  ACTION_SERVER_PATCH,\n  ACTION_RESTORE,\n  ACTION_REFRESH,\n  ACTION_PREFETCH,\n  ACTION_HMR_REFRESH,\n  ACTION_SERVER_ACTION,\n} from './router-reducer-types'\nimport type {\n  ReducerActions,\n  ReducerState,\n  ReadonlyReducerState,\n} from './router-reducer-types'\nimport { navigateReducer } from './reducers/navigate-reducer'\nimport { serverPatchReducer } from './reducers/server-patch-reducer'\nimport { restoreReducer } from './reducers/restore-reducer'\nimport { refreshReducer } from './reducers/refresh-reducer'\nimport { prefetchReducer } from './reducers/prefetch-reducer'\nimport { hmrRefreshReducer } from './reducers/hmr-refresh-reducer'\nimport { serverActionReducer } from './reducers/server-action-reducer'\n\n/**\n * Reducer that handles the app-router state updates.\n */\nfunction clientReducer(\n  state: ReadonlyReducerState,\n  action: ReducerActions\n): ReducerState {\n  switch (action.type) {\n    case ACTION_NAVIGATE: {\n      return navigateReducer(state, action)\n    }\n    case ACTION_SERVER_PATCH: {\n      return serverPatchReducer(state, action)\n    }\n    case ACTION_RESTORE: {\n      return restoreReducer(state, action)\n    }\n    case ACTION_REFRESH: {\n      return refreshReducer(state, action)\n    }\n    case ACTION_HMR_REFRESH: {\n      return hmrRefreshReducer(state, action)\n    }\n    case ACTION_PREFETCH: {\n      return prefetchReducer(state, action)\n    }\n    case ACTION_SERVER_ACTION: {\n      return serverActionReducer(state, action)\n    }\n    // This case should never be hit as dispatch is strongly typed.\n    default:\n      throw new Error('Unknown action')\n  }\n}\n\nfunction serverReducer(\n  state: ReadonlyReducerState,\n  _action: ReducerActions\n): ReducerState {\n  return state\n}\n\n// we don't run the client reducer on the server, so we use a noop function for better tree shaking\nexport const reducer =\n  typeof window === 'undefined' ? serverReducer : clientReducer\n"], "names": ["reducer", "clientReducer", "state", "action", "type", "ACTION_NAVIGATE", "navigateReducer", "ACTION_SERVER_PATCH", "serverPatchReducer", "ACTION_RESTORE", "restoreReducer", "ACTION_REFRESH", "refreshReducer", "ACTION_HMR_REFRESH", "hmrRefreshReducer", "ACTION_PREFETCH", "prefetchReducer", "ACTION_SERVER_ACTION", "serverActionReducer", "Error", "serverReducer", "_action", "window"], "mappings": ";;;;+BAiEaA;;;eAAAA;;;oCAzDN;iCAMyB;oCACG;gCACJ;gCACA;iCACC;mCACE;qCACE;AAEpC;;CAEC,GACD,SAASC,cACPC,KAA2B,EAC3BC,MAAsB;IAEtB,OAAQA,OAAOC,IAAI;QACjB,KAAKC,mCAAe;YAAE;gBACpB,OAAOC,IAAAA,gCAAe,EAACJ,OAAOC;YAChC;QACA,KAAKI,uCAAmB;YAAE;gBACxB,OAAOC,IAAAA,sCAAkB,EAACN,OAAOC;YACnC;QACA,KAAKM,kCAAc;YAAE;gBACnB,OAAOC,IAAAA,8BAAc,EAACR,OAAOC;YAC/B;QACA,KAAKQ,kCAAc;YAAE;gBACnB,OAAOC,IAAAA,8BAAc,EAACV,OAAOC;YAC/B;QACA,KAAKU,sCAAkB;YAAE;gBACvB,OAAOC,IAAAA,oCAAiB,EAACZ,OAAOC;YAClC;QACA,KAAKY,mCAAe;YAAE;gBACpB,OAAOC,IAAAA,gCAAe,EAACd,OAAOC;YAChC;QACA,KAAKc,wCAAoB;YAAE;gBACzB,OAAOC,IAAAA,wCAAmB,EAAChB,OAAOC;YACpC;QACA,+DAA+D;QAC/D;YACE,MAAM,qBAA2B,CAA3B,IAAIgB,MAAM,mBAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0B;IACpC;AACF;AAEA,SAASC,cACPlB,KAA2B,EAC3BmB,OAAuB;IAEvB,OAAOnB;AACT;AAGO,MAAMF,UACX,OAAOsB,WAAW,cAAcF,gBAAgBnB", "ignoreList": [0]}