{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-title.ts"], "sourcesContent": ["import type { Metadata } from '../types/metadata-interface'\nimport type { AbsoluteTemplateString } from '../types/metadata-types'\n\nfunction resolveTitleTemplate(\n  template: string | null | undefined,\n  title: string\n) {\n  return template ? template.replace(/%s/g, title) : title\n}\n\nexport function resolveTitle(\n  title: Metadata['title'],\n  stashedTemplate: string | null | undefined\n): AbsoluteTemplateString {\n  let resolved\n  const template =\n    typeof title !== 'string' && title && 'template' in title\n      ? title.template\n      : null\n\n  if (typeof title === 'string') {\n    resolved = resolveTitleTemplate(stashedTemplate, title)\n  } else if (title) {\n    if ('default' in title) {\n      resolved = resolveTitleTemplate(stashedTemplate, title.default)\n    }\n    if ('absolute' in title && title.absolute) {\n      resolved = title.absolute\n    }\n  }\n\n  if (title && typeof title !== 'string') {\n    return {\n      template,\n      absolute: resolved || '',\n    }\n  } else {\n    return { absolute: resolved || title || '', template }\n  }\n}\n"], "names": ["resolveTitleTemplate", "template", "title", "replace", "resolveTitle", "stashedTemplate", "resolved", "default", "absolute"], "mappings": "AAGA,SAASA,qBACPC,QAAmC,EACnCC,KAAa;IAEb,OAAOD,WAAWA,SAASE,OAAO,CAAC,OAAOD,SAASA;AACrD;AAEA,OAAO,SAASE,aACdF,KAAwB,EACxBG,eAA0C;IAE1C,IAAIC;IACJ,MAAML,WACJ,OAAOC,UAAU,YAAYA,SAAS,cAAcA,QAChDA,MAAMD,QAAQ,GACd;IAEN,IAAI,OAAOC,UAAU,UAAU;QAC7BI,WAAWN,qBAAqBK,iBAAiBH;IACnD,OAAO,IAAIA,OAAO;QAChB,IAAI,aAAaA,OAAO;YACtBI,WAAWN,qBAAqBK,iBAAiBH,MAAMK,OAAO;QAChE;QACA,IAAI,cAAcL,SAASA,MAAMM,QAAQ,EAAE;YACzCF,WAAWJ,MAAMM,QAAQ;QAC3B;IACF;IAEA,IAAIN,SAAS,OAAOA,UAAU,UAAU;QACtC,OAAO;YACLD;YACAO,UAAUF,YAAY;QACxB;IACF,OAAO;QACL,OAAO;YAAEE,UAAUF,YAAYJ,SAAS;YAAID;QAAS;IACvD;AACF", "ignoreList": [0]}