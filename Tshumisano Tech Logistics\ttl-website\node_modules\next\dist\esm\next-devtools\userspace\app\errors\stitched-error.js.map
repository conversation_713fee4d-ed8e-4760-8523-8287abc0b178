{"version": 3, "sources": ["../../../../../src/next-devtools/userspace/app/errors/stitched-error.ts"], "sourcesContent": ["import React from 'react'\nimport isError from '../../../../lib/is-error'\n\nconst ownerStacks = new WeakMap<Error, string | null>()\n\nexport function getOwnerStack(error: Error): string | null | undefined {\n  return ownerStacks.get(error)\n}\nexport function setOwnerStack(error: Error, stack: string | null) {\n  ownerStacks.set(error, stack)\n}\n\nexport function coerceError(value: unknown): Error {\n  return isError(value) ? value : new Error('' + value)\n}\n\nexport function setOwnerStackIfAvailable(error: Error): void {\n  // React 18 and prod does not have `captureOwnerStack`\n  if ('captureOwnerStack' in React) {\n    setOwnerStack(error, React.captureOwnerStack())\n  }\n}\n\nexport function decorateDevError(thrownValue: unknown) {\n  const error = coerceError(thrownValue)\n  setOwnerStackIfAvailable(error)\n  return error\n}\n"], "names": ["React", "isError", "ownerStacks", "WeakMap", "getOwnerStack", "error", "get", "setOwnerStack", "stack", "set", "coerceError", "value", "Error", "setOwnerStackIfAvailable", "captureOwnerStack", "decorateDevError", "thrownValue"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,aAAa,2BAA0B;AAE9C,MAAMC,cAAc,IAAIC;AAExB,OAAO,SAASC,cAAcC,KAAY;IACxC,OAAOH,YAAYI,GAAG,CAACD;AACzB;AACA,OAAO,SAASE,cAAcF,KAAY,EAAEG,KAAoB;IAC9DN,YAAYO,GAAG,CAACJ,OAAOG;AACzB;AAEA,OAAO,SAASE,YAAYC,KAAc;IACxC,OAAOV,QAAQU,SAASA,QAAQ,qBAAqB,CAArB,IAAIC,MAAM,KAAKD,QAAf,qBAAA;eAAA;oBAAA;sBAAA;IAAoB;AACtD;AAEA,OAAO,SAASE,yBAAyBR,KAAY;IACnD,sDAAsD;IACtD,IAAI,uBAAuBL,OAAO;QAChCO,cAAcF,OAAOL,MAAMc,iBAAiB;IAC9C;AACF;AAEA,OAAO,SAASC,iBAAiBC,WAAoB;IACnD,MAAMX,QAAQK,YAAYM;IAC1BH,yBAAyBR;IACzB,OAAOA;AACT", "ignoreList": [0]}