{"version": 3, "sources": ["../../src/server/render-result.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders, ServerResponse } from 'http'\nimport type { CacheControl } from './lib/cache-control'\nimport type { FetchMetrics } from './base-http'\n\nimport {\n  chainStreams,\n  streamFromBuffer,\n  streamFromString,\n  streamToString,\n} from './stream-utils/node-web-streams-helper'\nimport { isAbortError, pipeToNodeResponse } from './pipe-readable'\nimport type { RenderResumeDataCache } from './resume-data-cache/resume-data-cache'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport type {\n  HTML_CONTENT_TYPE_HEADER,\n  JSON_CONTENT_TYPE_HEADER,\n  TEXT_PLAIN_CONTENT_TYPE_HEADER,\n} from '../lib/constants'\nimport type { RSC_CONTENT_TYPE_HEADER } from '../client/components/app-router-headers'\n\ntype ContentTypeOption =\n  | typeof RSC_CONTENT_TYPE_HEADER // For App Page RSC responses\n  | typeof HTML_CONTENT_TYPE_HEADER // For App Page, Pages HTML responses\n  | typeof JSON_CONTENT_TYPE_HEADER // For API routes, Next.js data requests\n  | typeof TEXT_PLAIN_CONTENT_TYPE_HEADER // For simplified errors\n\nexport type AppPageRenderResultMetadata = {\n  flightData?: Buffer\n  cacheControl?: CacheControl\n  staticBailoutInfo?: {\n    stack?: string\n    description?: string\n  }\n\n  /**\n   * The postponed state if the render had postponed and needs to be resumed.\n   */\n  postponed?: string\n\n  /**\n   * The headers to set on the response that were added by the render.\n   */\n  headers?: OutgoingHttpHeaders\n  statusCode?: number\n  fetchTags?: string\n  fetchMetrics?: FetchMetrics\n\n  segmentData?: Map<string, Buffer>\n\n  /**\n   * In development, the resume data cache is warmed up before the render. This\n   * is attached to the metadata so that it can be used during the render. When\n   * prerendering, the filled resume data cache is also attached to the metadata\n   * so that it can be used when prerendering matching fallback shells.\n   */\n  renderResumeDataCache?: RenderResumeDataCache\n}\n\nexport type PagesRenderResultMetadata = {\n  pageData?: any\n  cacheControl?: CacheControl\n  assetQueryString?: string\n  isNotFound?: boolean\n  isRedirect?: boolean\n}\n\nexport type StaticRenderResultMetadata = {}\n\nexport type RenderResultMetadata = AppPageRenderResultMetadata &\n  PagesRenderResultMetadata &\n  StaticRenderResultMetadata\n\nexport type RenderResultResponse =\n  | ReadableStream<Uint8Array>[]\n  | ReadableStream<Uint8Array>\n  | string\n  | Buffer\n  | null\n\nexport type RenderResultOptions<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> = {\n  contentType: ContentTypeOption | null\n  waitUntil?: Promise<unknown>\n  metadata: Metadata\n}\n\nexport default class RenderResult<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> {\n  /**\n   * The detected content type for the response. This is used to set the\n   * `Content-Type` header.\n   */\n  public readonly contentType: ContentTypeOption | null\n\n  /**\n   * The metadata for the response. This is used to set the revalidation times\n   * and other metadata.\n   */\n  public readonly metadata: Readonly<Metadata>\n\n  /**\n   * The response itself. This can be a string, a stream, or null. If it's a\n   * string, then it's a static response. If it's a stream, then it's a\n   * dynamic response. If it's null, then the response was not found or was\n   * already sent.\n   */\n  private response: RenderResultResponse\n\n  /**\n   * A render result that represents an empty response. This is used to\n   * represent a response that was not found or was already sent.\n   */\n  public static readonly EMPTY = new RenderResult<StaticRenderResultMetadata>(\n    null,\n    { metadata: {}, contentType: null }\n  )\n\n  /**\n   * Creates a new RenderResult instance from a static response.\n   *\n   * @param value the static response value\n   * @param contentType the content type of the response\n   * @returns a new RenderResult instance\n   */\n  public static fromStatic(\n    value: string | Buffer,\n    contentType: ContentTypeOption\n  ) {\n    return new RenderResult<StaticRenderResultMetadata>(value, {\n      metadata: {},\n      contentType,\n    })\n  }\n\n  private readonly waitUntil?: Promise<unknown>\n\n  constructor(\n    response: RenderResultResponse,\n    { contentType, waitUntil, metadata }: RenderResultOptions<Metadata>\n  ) {\n    this.response = response\n    this.contentType = contentType\n    this.metadata = metadata\n    this.waitUntil = waitUntil\n  }\n\n  public assignMetadata(metadata: Metadata) {\n    Object.assign(this.metadata, metadata)\n  }\n\n  /**\n   * Returns true if the response is null. It can be null if the response was\n   * not found or was already sent.\n   */\n  public get isNull(): boolean {\n    return this.response === null\n  }\n\n  /**\n   * Returns false if the response is a string. It can be a string if the page\n   * was prerendered. If it's not, then it was generated dynamically.\n   */\n  public get isDynamic(): boolean {\n    return typeof this.response !== 'string'\n  }\n\n  /**\n   * Returns the response if it is a string. If the page was dynamic, this will\n   * return a promise if the `stream` option is true, or it will throw an error.\n   *\n   * @param stream Whether or not to return a promise if the response is dynamic\n   * @returns The response as a string\n   */\n  public toUnchunkedString(stream?: false): string\n  public toUnchunkedString(stream: true): Promise<string>\n  public toUnchunkedString(stream = false): Promise<string> | string {\n    if (this.response === null) {\n      // If the response is null, return an empty string. This behavior is\n      // intentional as we're now providing the `RenderResult.EMPTY` value.\n      return ''\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new InvariantError(\n          'dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToString(this.readable)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Returns a readable stream of the response.\n   */\n  private get readable(): ReadableStream<Uint8Array> {\n    if (this.response === null) {\n      // If the response is null, return an empty stream. This behavior is\n      // intentional as we're now providing the `RenderResult.EMPTY` value.\n      return new ReadableStream<Uint8Array>({\n        start(controller) {\n          controller.close()\n        },\n      })\n    }\n\n    if (typeof this.response === 'string') {\n      return streamFromString(this.response)\n    }\n\n    if (Buffer.isBuffer(this.response)) {\n      return streamFromBuffer(this.response)\n    }\n\n    // If the response is an array of streams, then chain them together.\n    if (Array.isArray(this.response)) {\n      return chainStreams(...this.response)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Coerces the response to an array of streams. This will convert the response\n   * to an array of streams if it is not already one.\n   *\n   * @returns An array of streams\n   */\n  private coerce(): ReadableStream<Uint8Array>[] {\n    if (this.response === null) {\n      // If the response is null, return an empty stream. This behavior is\n      // intentional as we're now providing the `RenderResult.EMPTY` value.\n      return []\n    }\n\n    if (typeof this.response === 'string') {\n      return [streamFromString(this.response)]\n    } else if (Array.isArray(this.response)) {\n      return this.response\n    } else if (Buffer.isBuffer(this.response)) {\n      return [streamFromBuffer(this.response)]\n    } else {\n      return [this.response]\n    }\n  }\n\n  /**\n   * Unshifts a new stream to the response. This will convert the response to an\n   * array of streams if it is not already one and will add the new stream to\n   * the start of the array. When this response is piped, all of the streams\n   * will be piped one after the other.\n   *\n   * @param readable The new stream to unshift\n   */\n  public unshift(readable: ReadableStream<Uint8Array>): void {\n    // Coerce the response to an array of streams.\n    this.response = this.coerce()\n\n    // Add the new stream to the start of the array.\n    this.response.unshift(readable)\n  }\n\n  /**\n   * Chains a new stream to the response. This will convert the response to an\n   * array of streams if it is not already one and will add the new stream to\n   * the end. When this response is piped, all of the streams will be piped\n   * one after the other.\n   *\n   * @param readable The new stream to chain\n   */\n  public push(readable: ReadableStream<Uint8Array>): void {\n    // Coerce the response to an array of streams.\n    this.response = this.coerce()\n\n    // Add the new stream to the end of the array.\n    this.response.push(readable)\n  }\n\n  /**\n   * Pipes the response to a writable stream. This will close/cancel the\n   * writable stream if an error is encountered. If this doesn't throw, then\n   * the writable stream will be closed or aborted.\n   *\n   * @param writable Writable stream to pipe the response to\n   */\n  public async pipeTo(writable: WritableStream<Uint8Array>): Promise<void> {\n    try {\n      await this.readable.pipeTo(writable, {\n        // We want to close the writable stream ourselves so that we can wait\n        // for the waitUntil promise to resolve before closing it. If an error\n        // is encountered, we'll abort the writable stream if we swallowed the\n        // error.\n        preventClose: true,\n      })\n\n      // If there is a waitUntil promise, wait for it to resolve before\n      // closing the writable stream.\n      if (this.waitUntil) await this.waitUntil\n\n      // Close the writable stream.\n      await writable.close()\n    } catch (err) {\n      // If this is an abort error, we should abort the writable stream (as we\n      // took ownership of it when we started piping). We don't need to re-throw\n      // because we handled the error.\n      if (isAbortError(err)) {\n        // Abort the writable stream if an error is encountered.\n        await writable.abort(err)\n\n        return\n      }\n\n      // We're not aborting the writer here as when this method throws it's not\n      // clear as to how so the caller should assume it's their responsibility\n      // to clean up the writer.\n      throw err\n    }\n  }\n\n  /**\n   * Pipes the response to a node response. This will close/cancel the node\n   * response if an error is encountered.\n   *\n   * @param res\n   */\n  public async pipeToNodeResponse(res: ServerResponse) {\n    await pipeToNodeResponse(this.readable, res, this.waitUntil)\n  }\n}\n"], "names": ["chainStreams", "streamFromBuffer", "streamFromString", "streamToString", "isAbortError", "pipeToNodeResponse", "InvariantError", "RenderResult", "EMPTY", "metadata", "contentType", "fromStatic", "value", "constructor", "response", "waitUntil", "assignMetadata", "Object", "assign", "isNull", "isDynamic", "toUnchunkedString", "stream", "readable", "ReadableStream", "start", "controller", "close", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "coerce", "unshift", "push", "pipeTo", "writable", "preventClose", "err", "abort", "res"], "mappings": "AAIA,SACEA,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,QACT,yCAAwC;AAC/C,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,kBAAiB;AAElE,SAASC,cAAc,QAAQ,gCAA+B;AA2E9D,eAAe,MAAMC;gBAuBnB;;;GAGC,QACsBC,QAAQ,IAAID,aACjC,MACA;QAAEE,UAAU,CAAC;QAAGC,aAAa;IAAK;IAGpC;;;;;;GAMC,GACD,OAAcC,WACZC,KAAsB,EACtBF,WAA8B,EAC9B;QACA,OAAO,IAAIH,aAAyCK,OAAO;YACzDH,UAAU,CAAC;YACXC;QACF;IACF;IAIAG,YACEC,QAA8B,EAC9B,EAAEJ,WAAW,EAAEK,SAAS,EAAEN,QAAQ,EAAiC,CACnE;QACA,IAAI,CAACK,QAAQ,GAAGA;QAChB,IAAI,CAACJ,WAAW,GAAGA;QACnB,IAAI,CAACD,QAAQ,GAAGA;QAChB,IAAI,CAACM,SAAS,GAAGA;IACnB;IAEOC,eAAeP,QAAkB,EAAE;QACxCQ,OAAOC,MAAM,CAAC,IAAI,CAACT,QAAQ,EAAEA;IAC/B;IAEA;;;GAGC,GACD,IAAWU,SAAkB;QAC3B,OAAO,IAAI,CAACL,QAAQ,KAAK;IAC3B;IAEA;;;GAGC,GACD,IAAWM,YAAqB;QAC9B,OAAO,OAAO,IAAI,CAACN,QAAQ,KAAK;IAClC;IAWOO,kBAAkBC,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACR,QAAQ,KAAK,MAAM;YAC1B,oEAAoE;YACpE,qEAAqE;YACrE,OAAO;QACT;QAEA,IAAI,OAAO,IAAI,CAACA,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACQ,QAAQ;gBACX,MAAM,qBAEL,CAFK,IAAIhB,eACR,oEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,OAAOH,eAAe,IAAI,CAACoB,QAAQ;QACrC;QAEA,OAAO,IAAI,CAACT,QAAQ;IACtB;IAEA;;GAEC,GACD,IAAYS,WAAuC;QACjD,IAAI,IAAI,CAACT,QAAQ,KAAK,MAAM;YAC1B,oEAAoE;YACpE,qEAAqE;YACrE,OAAO,IAAIU,eAA2B;gBACpCC,OAAMC,UAAU;oBACdA,WAAWC,KAAK;gBAClB;YACF;QACF;QAEA,IAAI,OAAO,IAAI,CAACb,QAAQ,KAAK,UAAU;YACrC,OAAOZ,iBAAiB,IAAI,CAACY,QAAQ;QACvC;QAEA,IAAIc,OAAOC,QAAQ,CAAC,IAAI,CAACf,QAAQ,GAAG;YAClC,OAAOb,iBAAiB,IAAI,CAACa,QAAQ;QACvC;QAEA,oEAAoE;QACpE,IAAIgB,MAAMC,OAAO,CAAC,IAAI,CAACjB,QAAQ,GAAG;YAChC,OAAOd,gBAAgB,IAAI,CAACc,QAAQ;QACtC;QAEA,OAAO,IAAI,CAACA,QAAQ;IACtB;IAEA;;;;;GAKC,GACD,AAAQkB,SAAuC;QAC7C,IAAI,IAAI,CAAClB,QAAQ,KAAK,MAAM;YAC1B,oEAAoE;YACpE,qEAAqE;YACrE,OAAO,EAAE;QACX;QAEA,IAAI,OAAO,IAAI,CAACA,QAAQ,KAAK,UAAU;YACrC,OAAO;gBAACZ,iBAAiB,IAAI,CAACY,QAAQ;aAAE;QAC1C,OAAO,IAAIgB,MAAMC,OAAO,CAAC,IAAI,CAACjB,QAAQ,GAAG;YACvC,OAAO,IAAI,CAACA,QAAQ;QACtB,OAAO,IAAIc,OAAOC,QAAQ,CAAC,IAAI,CAACf,QAAQ,GAAG;YACzC,OAAO;gBAACb,iBAAiB,IAAI,CAACa,QAAQ;aAAE;QAC1C,OAAO;YACL,OAAO;gBAAC,IAAI,CAACA,QAAQ;aAAC;QACxB;IACF;IAEA;;;;;;;GAOC,GACD,AAAOmB,QAAQV,QAAoC,EAAQ;QACzD,8CAA8C;QAC9C,IAAI,CAACT,QAAQ,GAAG,IAAI,CAACkB,MAAM;QAE3B,gDAAgD;QAChD,IAAI,CAAClB,QAAQ,CAACmB,OAAO,CAACV;IACxB;IAEA;;;;;;;GAOC,GACD,AAAOW,KAAKX,QAAoC,EAAQ;QACtD,8CAA8C;QAC9C,IAAI,CAACT,QAAQ,GAAG,IAAI,CAACkB,MAAM;QAE3B,8CAA8C;QAC9C,IAAI,CAAClB,QAAQ,CAACoB,IAAI,CAACX;IACrB;IAEA;;;;;;GAMC,GACD,MAAaY,OAAOC,QAAoC,EAAiB;QACvE,IAAI;YACF,MAAM,IAAI,CAACb,QAAQ,CAACY,MAAM,CAACC,UAAU;gBACnC,qEAAqE;gBACrE,sEAAsE;gBACtE,sEAAsE;gBACtE,SAAS;gBACTC,cAAc;YAChB;YAEA,iEAAiE;YACjE,+BAA+B;YAC/B,IAAI,IAAI,CAACtB,SAAS,EAAE,MAAM,IAAI,CAACA,SAAS;YAExC,6BAA6B;YAC7B,MAAMqB,SAAST,KAAK;QACtB,EAAE,OAAOW,KAAK;YACZ,wEAAwE;YACxE,0EAA0E;YAC1E,gCAAgC;YAChC,IAAIlC,aAAakC,MAAM;gBACrB,wDAAwD;gBACxD,MAAMF,SAASG,KAAK,CAACD;gBAErB;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,MAAMA;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAajC,mBAAmBmC,GAAmB,EAAE;QACnD,MAAMnC,mBAAmB,IAAI,CAACkB,QAAQ,EAAEiB,KAAK,IAAI,CAACzB,SAAS;IAC7D;AACF", "ignoreList": [0]}