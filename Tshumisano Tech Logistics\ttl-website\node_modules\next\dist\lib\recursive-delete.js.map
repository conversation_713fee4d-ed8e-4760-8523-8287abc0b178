{"version": 3, "sources": ["../../src/lib/recursive-delete.ts"], "sourcesContent": ["import type { Dirent } from 'fs'\nimport { promises } from 'fs'\nimport { join, isAbsolute, dirname } from 'path'\nimport isError from './is-error'\nimport { wait } from './wait'\n\nconst unlinkPath = async (p: string, isDir = false, t = 1): Promise<void> => {\n  try {\n    if (isDir) {\n      await promises.rmdir(p)\n    } else {\n      await promises.unlink(p)\n    }\n  } catch (e) {\n    const code = isError(e) && e.code\n    if (\n      (code === 'EBUSY' ||\n        code === 'ENOTEMPTY' ||\n        code === 'EPERM' ||\n        code === 'EMFILE') &&\n      t < 3\n    ) {\n      await wait(t * 100)\n      return unlinkPath(p, isDir, t++)\n    }\n\n    if (code === 'ENOENT') {\n      return\n    }\n\n    throw e\n  }\n}\n\n/**\n * Recursively delete directory contents\n */\nexport async function recursiveDelete(\n  /** Directory to delete the contents of */\n  dir: string,\n  /** Exclude based on relative file path */\n  exclude?: RegExp,\n  /** Ensures that parameter dir exists, this is not passed recursively */\n  previousPath: string = ''\n): Promise<void> {\n  let result\n  try {\n    result = await promises.readdir(dir, { withFileTypes: true })\n  } catch (e) {\n    if (isError(e) && e.code === 'ENOENT') {\n      return\n    }\n    throw e\n  }\n\n  await Promise.all(\n    result.map(async (part: Dirent) => {\n      const absolutePath = join(dir, part.name)\n\n      // readdir does not follow symbolic links\n      // if part is a symbolic link, follow it using stat\n      let isDirectory = part.isDirectory()\n      const isSymlink = part.isSymbolicLink()\n\n      if (isSymlink) {\n        const linkPath = await promises.readlink(absolutePath)\n\n        try {\n          const stats = await promises.stat(\n            isAbsolute(linkPath)\n              ? linkPath\n              : join(dirname(absolutePath), linkPath)\n          )\n          isDirectory = stats.isDirectory()\n        } catch {}\n      }\n\n      const pp = join(previousPath, part.name)\n      const isNotExcluded = !exclude || !exclude.test(pp)\n\n      if (isNotExcluded) {\n        if (!isSymlink && isDirectory) {\n          await recursiveDelete(absolutePath, exclude, pp)\n        }\n        return unlinkPath(absolutePath, !isSymlink && isDirectory)\n      }\n    })\n  )\n}\n"], "names": ["recursiveDelete", "unlinkPath", "p", "isDir", "t", "promises", "rmdir", "unlink", "e", "code", "isError", "wait", "dir", "exclude", "previousPath", "result", "readdir", "withFileTypes", "Promise", "all", "map", "part", "absolutePath", "join", "name", "isDirectory", "isSymlink", "isSymbolicLink", "linkPath", "readlink", "stats", "stat", "isAbsolute", "dirname", "pp", "isNotExcluded", "test"], "mappings": ";;;;+BAqCsBA;;;eAAAA;;;oBApCG;sBACiB;gEACtB;sBACC;;;;;;AAErB,MAAMC,aAAa,OAAOC,GAAWC,QAAQ,KAAK,EAAEC,IAAI,CAAC;IACvD,IAAI;QACF,IAAID,OAAO;YACT,MAAME,YAAQ,CAACC,KAAK,CAACJ;QACvB,OAAO;YACL,MAAMG,YAAQ,CAACE,MAAM,CAACL;QACxB;IACF,EAAE,OAAOM,GAAG;QACV,MAAMC,OAAOC,IAAAA,gBAAO,EAACF,MAAMA,EAAEC,IAAI;QACjC,IACE,AAACA,CAAAA,SAAS,WACRA,SAAS,eACTA,SAAS,WACTA,SAAS,QAAO,KAClBL,IAAI,GACJ;YACA,MAAMO,IAAAA,UAAI,EAACP,IAAI;YACf,OAAOH,WAAWC,GAAGC,OAAOC;QAC9B;QAEA,IAAIK,SAAS,UAAU;YACrB;QACF;QAEA,MAAMD;IACR;AACF;AAKO,eAAeR,gBACpB,wCAAwC,GACxCY,GAAW,EACX,wCAAwC,GACxCC,OAAgB,EAChB,sEAAsE,GACtEC,eAAuB,EAAE;IAEzB,IAAIC;IACJ,IAAI;QACFA,SAAS,MAAMV,YAAQ,CAACW,OAAO,CAACJ,KAAK;YAAEK,eAAe;QAAK;IAC7D,EAAE,OAAOT,GAAG;QACV,IAAIE,IAAAA,gBAAO,EAACF,MAAMA,EAAEC,IAAI,KAAK,UAAU;YACrC;QACF;QACA,MAAMD;IACR;IAEA,MAAMU,QAAQC,GAAG,CACfJ,OAAOK,GAAG,CAAC,OAAOC;QAChB,MAAMC,eAAeC,IAAAA,UAAI,EAACX,KAAKS,KAAKG,IAAI;QAExC,yCAAyC;QACzC,mDAAmD;QACnD,IAAIC,cAAcJ,KAAKI,WAAW;QAClC,MAAMC,YAAYL,KAAKM,cAAc;QAErC,IAAID,WAAW;YACb,MAAME,WAAW,MAAMvB,YAAQ,CAACwB,QAAQ,CAACP;YAEzC,IAAI;gBACF,MAAMQ,QAAQ,MAAMzB,YAAQ,CAAC0B,IAAI,CAC/BC,IAAAA,gBAAU,EAACJ,YACPA,WACAL,IAAAA,UAAI,EAACU,IAAAA,aAAO,EAACX,eAAeM;gBAElCH,cAAcK,MAAML,WAAW;YACjC,EAAE,OAAM,CAAC;QACX;QAEA,MAAMS,KAAKX,IAAAA,UAAI,EAACT,cAAcO,KAAKG,IAAI;QACvC,MAAMW,gBAAgB,CAACtB,WAAW,CAACA,QAAQuB,IAAI,CAACF;QAEhD,IAAIC,eAAe;YACjB,IAAI,CAACT,aAAaD,aAAa;gBAC7B,MAAMzB,gBAAgBsB,cAAcT,SAASqB;YAC/C;YACA,OAAOjC,WAAWqB,cAAc,CAACI,aAAaD;QAChD;IACF;AAEJ", "ignoreList": [0]}