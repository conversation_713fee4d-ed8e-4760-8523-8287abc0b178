import type { IncomingMessage, ServerResponse } from 'node:http';
import type { InstrumentationOnRequestError, RequestErrorContext } from '../instrumentation/types';
import type { ParsedUrlQuery } from 'node:querystring';
import type { UrlWithParsedQuery } from 'node:url';
import type { PrerenderManifest, RequiredServerFilesManifest } from '../../build';
import type { DevRoutesManifest } from '../lib/router-utils/setup-dev-bundler';
import type { RouteDefinition } from '../route-definitions/route-definition';
import type { DeepReadonly } from '../../shared/lib/deep-readonly';
import type { PreviewData } from '../../types';
import type { BuildManifest } from '../get-page-files';
import type { ReactLoadableManifest } from '../load-components';
import type { NextFontManifest } from '../../build/webpack/plugins/next-font-manifest-plugin';
import { IncrementalCache } from '../lib/incremental-cache';
import type { RouteKind } from '../route-kind';
import type { BaseNextRequest } from '../base-http';
import type { NextConfigComplete } from '../config-shared';
import ResponseCache, { type ResponseGenerator } from '../response-cache';
import { type RouterServerContext } from '../lib/router-utils/router-server-context';
/**
 * RouteModuleOptions is the options that are passed to the route module, other
 * route modules should extend this class to add specific options for their
 * route.
 */
export interface RouteModuleOptions<D extends RouteDefinition = RouteDefinition, U = unknown> {
    readonly definition: Readonly<D>;
    readonly userland: Readonly<U>;
    readonly distDir: string;
    readonly relativeProjectDir: string;
}
/**
 * RouteHandlerContext is the base context for a route handler.
 */
export interface RouteModuleHandleContext {
    /**
     * Any matched parameters for the request. This is only defined for dynamic
     * routes.
     */
    params: Record<string, string | string[] | undefined> | undefined;
}
/**
 * RouteModule is the base class for all route modules. This class should be
 * extended by all route modules.
 */
export declare abstract class RouteModule<D extends RouteDefinition = RouteDefinition, U = unknown> {
    /**
     * The userland module. This is the module that is exported from the user's
     * code. This is marked as readonly to ensure that the module is not mutated
     * because the module (when compiled) only provides getters.
     */
    readonly userland: Readonly<U>;
    /**
     * The definition of the route.
     */
    readonly definition: Readonly<D>;
    /**
     * The shared modules that are exposed and required for the route module.
     */
    static readonly sharedModules: any;
    isDev: boolean;
    distDir: string;
    isAppRouter?: boolean;
    relativeProjectDir: string;
    incrementCache?: IncrementalCache;
    responseCache?: ResponseCache;
    constructor({ userland, definition, distDir, relativeProjectDir, }: RouteModuleOptions<D, U>);
    instrumentationOnRequestError(req: IncomingMessage | BaseNextRequest, ...args: Parameters<InstrumentationOnRequestError>): Promise<void>;
    private loadManifests;
    loadCustomCacheHandlers(req: IncomingMessage | BaseNextRequest, nextConfig: NextConfigComplete): Promise<void>;
    getIncrementalCache(req: IncomingMessage | BaseNextRequest, nextConfig: NextConfigComplete, prerenderManifest: DeepReadonly<PrerenderManifest>): Promise<IncrementalCache>;
    onRequestError(req: IncomingMessage | BaseNextRequest, err: unknown, errorContext: RequestErrorContext, routerServerContext?: RouterServerContext[string]): Promise<void>;
    prepare(req: IncomingMessage | BaseNextRequest, res: ServerResponse | null, { srcPage, multiZoneDraftMode, }: {
        srcPage: string;
        multiZoneDraftMode?: boolean;
    }): Promise<{
        buildId: string;
        locale?: string;
        locales?: readonly string[];
        defaultLocale?: string;
        query: ParsedUrlQuery;
        originalQuery: ParsedUrlQuery;
        originalPathname: string;
        params?: ParsedUrlQuery;
        parsedUrl: UrlWithParsedQuery;
        previewData: PreviewData;
        pageIsDynamic: boolean;
        isDraftMode: boolean;
        resolvedPathname: string;
        isNextDataRequest: boolean;
        buildManifest: DeepReadonly<BuildManifest>;
        fallbackBuildManifest: DeepReadonly<BuildManifest>;
        nextFontManifest: DeepReadonly<NextFontManifest>;
        serverFilesManifest: DeepReadonly<RequiredServerFilesManifest>;
        reactLoadableManifest: DeepReadonly<ReactLoadableManifest>;
        routesManifest: DeepReadonly<DevRoutesManifest>;
        prerenderManifest: DeepReadonly<PrerenderManifest>;
        clientReferenceManifest?: any;
        serverActionsManifest?: any;
        dynamicCssManifest?: any;
        subresourceIntegrityManifest?: DeepReadonly<Record<string, string>>;
        isOnDemandRevalidate: boolean;
        revalidateOnlyGenerated: boolean;
        nextConfig: NextConfigComplete;
        routerServerContext?: RouterServerContext[string];
        interceptionRoutePatterns?: any;
    } | undefined>;
    getResponseCache(req: IncomingMessage | BaseNextRequest): ResponseCache;
    handleResponse({ req, nextConfig, cacheKey, routeKind, isFallback, prerenderManifest, isRoutePPREnabled, isOnDemandRevalidate, revalidateOnlyGenerated, responseGenerator, waitUntil, }: {
        req: IncomingMessage | BaseNextRequest;
        nextConfig: NextConfigComplete;
        cacheKey: string | null;
        routeKind: RouteKind;
        isFallback?: boolean;
        prerenderManifest: DeepReadonly<PrerenderManifest>;
        isRoutePPREnabled?: boolean;
        isOnDemandRevalidate?: boolean;
        revalidateOnlyGenerated?: boolean;
        responseGenerator: ResponseGenerator;
        waitUntil?: (prom: Promise<any>) => void;
    }): Promise<import("../response-cache").ResponseCacheEntry | null>;
}
