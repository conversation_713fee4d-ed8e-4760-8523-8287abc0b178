{"name": "ttl-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "15.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "@heroicons/react": "^2.0.18", "framer-motion": "^11.0.0", "lucide-react": "^0.263.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "15.5.2", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}